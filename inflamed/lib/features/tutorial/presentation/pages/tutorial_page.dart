import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class TutorialPage extends StatefulWidget {
  const TutorialPage({super.key});

  @override
  State<TutorialPage> createState() => _TutorialPageState();
}

class _TutorialPageState extends State<TutorialPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<TutorialData> _pages = [
    TutorialData(
      title: 'Track Your Health',
      description: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy text ever.',
      svgAsset: 'assets/images/OverviewTutorialScreens/tutorial_background_group_1.svg',
      backgroundAsset: 'assets/images/OverviewTutorialScreens/tutorial_background_1.svg',
    ),
    TutorialData(
      title: 'Monitor Symptoms',
      description: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy text ever.',
      svgAsset: 'assets/images/OverviewTutorialScreens/tutorial_background_group_2.svg',
      backgroundAsset: 'assets/images/OverviewTutorialScreens/tutorial_background_2.svg',
    ),
    TutorialData(
      title: 'Get Insights',
      description: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy text ever.',
      svgAsset: 'assets/images/OverviewTutorialScreens/tutorial_background_group_3.svg',
      backgroundAsset: 'assets/images/OverviewTutorialScreens/tutorial_background_3.svg',
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF2C2235) : const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: () {
                    _skipToHome();
                  },
                  child: Text(
                    'Skip',
                    style: TextStyle(
                      color: AppColors.textGuideButtonColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
            
            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index], isDark);
                },
              ),
            ),
            
            // Bottom section with indicators and buttons
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => _buildPageIndicator(index),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Buttons
                  Row(
                    children: [
                      // Back button (only show after first page)
                      if (_currentPage > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            style: OutlinedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              foregroundColor: isDark ? AppColors.primaryTextDark : AppColors.primaryText,
                              side: BorderSide(
                                color: isDark ? AppColors.themedBorderDark : AppColors.themedBorder,
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(28),
                              ),
                            ),
                            child: const Text('Back'),
                          ),
                        ),
                      
                      if (_currentPage > 0) const SizedBox(width: 16),
                      
                      // Next/Get Started button
                      Expanded(
                        flex: _currentPage == 0 ? 1 : 1,
                        child: ElevatedButton(
                          onPressed: () {
                            if (_currentPage < _pages.length - 1) {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            } else {
                              _completeOnboarding();
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDark ? AppColors.onboardingButtonBackground : AppColors.primary,
                            foregroundColor: isDark ? AppColors.primary : Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 18),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(28),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            _currentPage < _pages.length - 1 ? 'NEXT' : 'GET STARTED',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(TutorialData data, bool isDark) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        children: [
          // Background curved shapes - positioned below status bar
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                Positioned.fill(
                  child: SvgPicture.asset(
                    data.backgroundAsset!,
                    fit: BoxFit.contain,
                    alignment: Alignment.topCenter,
                  ),
                ),
              ],
            ),
          ),

          // Main illustration area
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30.0),
              child: Center(
                child: SizedBox(
                  height: 250,
                  width: 250,
                  child: SvgPicture.asset(
                    data.svgAsset,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ),

          // Text content area
          Expanded(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Description text
                  Text(
                    data.description,
                    style: TextStyle(
                      fontSize: 16,
                      color: isDark ? Colors.white : const Color(0xFF666666),
                      height: 1.6,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: _currentPage == index ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: _currentPage == index
            ? (isDark ? AppColors.onboardingButtonBackground : AppColors.primary)
            : (isDark ? AppColors.onboardingButtonBackground.withValues(alpha: 0.3) : AppColors.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  void _skipToHome() {
    Navigator.pushReplacementNamed(context, '/home');
  }

  void _completeOnboarding() {
    Navigator.pushReplacementNamed(context, '/home');
  }
}

class TutorialData {
  final String title;
  final String description;
  final String svgAsset;
  final String? backgroundAsset;

  TutorialData({
    required this.title,
    required this.description,
    required this.svgAsset,
    this.backgroundAsset,
  });
}
